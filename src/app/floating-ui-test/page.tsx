'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useFloatingUIElement, useFloatingUIManager } from '@/hooks/use-floating-ui';
import { Bell, Settings, HelpCircle, X } from 'lucide-react';

export default function FloatingUITestPage() {
	const [notificationCount, setNotificationCount] = useState(0);
	const [showSettings, setShowSettings] = useState(false);
	const [showGuidance, setShowGuidance] = useState(false);

	const { hideAll, showAll, visibleCount, totalCount } = useFloatingUIManager();

	// Test notification content
	const notificationContent = (
		<Card className="w-80 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center gap-2 text-sm">
					<Bell className="h-4 w-4" />
					Test Notification #{notificationCount}
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground">
					This is test notification #{notificationCount}. It should appear without causing
					infinite loops.
				</p>
			</CardContent>
		</Card>
	);

	// Use floating UI for notification
	const { show: showNotification } = useFloatingUIElement(
		`test-notification-${notificationCount}`,
		notificationContent,
		{
			type: 'notification',
			priority: 'medium',
			position: 'top-right',
			coordinates: { top: 16, right: 16 },
			animation: { type: 'slide', duration: 300 },
		}
	);

	const createNotification = () => {
		const count = notificationCount + 1;
		setNotificationCount(count);
		showNotification();
	};

	// Test settings panel
	const settingsContent = (
		<Card className="w-64 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center justify-between text-sm">
					<span className="flex items-center gap-2">
						<Settings className="h-4 w-4" />
						Settings Panel
					</span>
					<Button
						size="icon"
						variant="ghost"
						className="h-6 w-6"
						onClick={() => {
							hideSettingsPanel();
							setShowSettings(false);
						}}
					>
						<X className="h-3 w-3" />
					</Button>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground mb-3">This is a test settings panel.</p>
				<div className="space-y-2">
					<Button size="sm" variant="outline" className="w-full">
						Option 1
					</Button>
					<Button size="sm" variant="outline" className="w-full">
						Option 2
					</Button>
				</div>
			</CardContent>
		</Card>
	);

	const { show: showSettingsPanel, hide: hideSettingsPanel } = useFloatingUIElement(
		'test-settings',
		settingsContent,
		{
			type: 'settings',
			priority: 'high',
			position: 'bottom-right',
			coordinates: { bottom: 16, right: 16 },
			animation: { type: 'scale', duration: 200 },
		}
	);

	// Test guidance panel
	const guidanceContent = (
		<Card className="w-72 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center justify-between text-sm">
					<span className="flex items-center gap-2">
						<HelpCircle className="h-4 w-4" />
						Page Guidance
					</span>
					<Button
						size="icon"
						variant="ghost"
						className="h-6 w-6"
						onClick={() => {
							hideGuidancePanel();
							setShowGuidance(false);
						}}
					>
						<X className="h-3 w-3" />
					</Button>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground mb-3">
					This is a test guidance panel to help users understand the page.
				</p>
				<div className="space-y-2">
					<div className="flex items-center gap-2 text-sm">
						<span className="w-5 h-5 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
							1
						</span>
						<span>First step explanation</span>
					</div>
					<div className="flex items-center gap-2 text-sm">
						<span className="w-5 h-5 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
							2
						</span>
						<span>Second step explanation</span>
					</div>
				</div>
			</CardContent>
		</Card>
	);

	const { show: showGuidancePanel, hide: hideGuidancePanel } = useFloatingUIElement(
		'test-guidance',
		guidanceContent,
		{
			type: 'guidance',
			priority: 'medium',
			position: 'bottom-right',
			coordinates: { bottom: 80, right: 16 },
			animation: { type: 'slide', duration: 300 },
		}
	);

	return (
		<div className="container mx-auto p-6 space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Floating UI System Test</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{/* Notification Tests */}
						<div className="space-y-2">
							<h4 className="font-medium">Notifications</h4>
							<Button onClick={createNotification} className="w-full">
								<Bell className="h-4 w-4 mr-2" />
								Create Notification
							</Button>
							<p className="text-sm text-muted-foreground">
								Created: {notificationCount}
							</p>
						</div>

						{/* Settings Tests */}
						<div className="space-y-2">
							<h4 className="font-medium">Settings Panel</h4>
							<Button
								onClick={() => {
									if (showSettings) {
										hideSettingsPanel();
										setShowSettings(false);
									} else {
										showSettingsPanel();
										setShowSettings(true);
									}
								}}
								variant={showSettings ? 'default' : 'outline'}
								className="w-full"
							>
								<Settings className="h-4 w-4 mr-2" />
								{showSettings ? 'Hide' : 'Show'} Settings
							</Button>
						</div>

						{/* Guidance Tests */}
						<div className="space-y-2">
							<h4 className="font-medium">Page Guidance</h4>
							<Button
								onClick={() => {
									if (showGuidance) {
										hideGuidancePanel();
										setShowGuidance(false);
									} else {
										showGuidancePanel();
										setShowGuidance(true);
									}
								}}
								variant={showGuidance ? 'default' : 'outline'}
								className="w-full"
							>
								<HelpCircle className="h-4 w-4 mr-2" />
								{showGuidance ? 'Hide' : 'Show'} Guidance
							</Button>
						</div>
					</div>

					{/* Global Controls */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-3">Global Controls</h4>
						<div className="flex flex-wrap gap-2">
							<Button size="sm" variant="outline" onClick={() => showAll()}>
								Show All
							</Button>
							<Button size="sm" variant="outline" onClick={() => hideAll()}>
								Hide All
							</Button>
						</div>
					</div>

					{/* Status */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-2">System Status</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>Visible Elements: {visibleCount}</p>
							<p>Total Elements: {totalCount}</p>
						</div>
					</div>

					{/* Instructions */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-2">Test Instructions</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>
								1. Click &quot;Create Notification&quot; to test notification system
							</p>
							<p>2. Toggle Settings and Guidance panels</p>
							<p>3. Check that no infinite loops occur</p>
							<p>4. Verify animations work correctly</p>
							<p>5. Test collision detection with multiple elements</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
