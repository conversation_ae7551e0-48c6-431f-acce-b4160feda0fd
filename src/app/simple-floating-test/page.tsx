'use client';

import { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { useSimpleFloatingUI, useSimpleFloatingContext } from '@/contexts/simple-floating-context';
import { <PERSON>, Settings, HelpCircle, X } from 'lucide-react';

export default function SimpleFloatingTestPage() {
	const [notificationCount, setNotificationCount] = useState(0);
	const [showSettings, setShowSettings] = useState(false);
	const [showGuidance, setShowGuidance] = useState(false);

	const { hideAll, getVisibleElements } = useSimpleFloatingContext();

	// Test notification
	const notificationContent = useMemo(() => (
		<Card className="w-80 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center gap-2 text-sm">
					<Bell className="h-4 w-4" />
					Test Notification #{notificationCount}
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground">
					This is test notification #{notificationCount}. No infinite loops!
				</p>
			</CardContent>
		</Card>
	), [notificationCount]);

	const { show: showNotification, hide: hideNotification, isVisible: notificationVisible } = useSimpleFloatingUI(
		`test-notification-${notificationCount}`,
		notificationContent,
		{
			position: { top: 16, right: 16 },
			zIndex: 1100,
		}
	);

	// Test settings panel
	const settingsContent = useMemo(() => (
		<Card className="w-64 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center justify-between text-sm">
					<span className="flex items-center gap-2">
						<Settings className="h-4 w-4" />
						Settings Panel
					</span>
					<Button
						size="icon"
						variant="ghost"
						className="h-6 w-6"
						onClick={() => {
							hideSettings();
							setShowSettings(false);
						}}
					>
						<X className="h-3 w-3" />
					</Button>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground mb-3">
					This is a test settings panel using simple floating UI.
				</p>
				<div className="space-y-2">
					<Button size="sm" variant="outline" className="w-full">
						Option 1
					</Button>
					<Button size="sm" variant="outline" className="w-full">
						Option 2
					</Button>
				</div>
			</CardContent>
		</Card>
	), []);

	const { show: showSettingsPanel, hide: hideSettings, isVisible: settingsVisible } = useSimpleFloatingUI(
		'test-settings',
		settingsContent,
		{
			position: { bottom: 16, right: 16 },
			zIndex: 1200,
		}
	);

	// Test guidance panel
	const guidanceContent = useMemo(() => (
		<Card className="w-72 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center justify-between text-sm">
					<span className="flex items-center gap-2">
						<HelpCircle className="h-4 w-4" />
						Page Guidance
					</span>
					<Button
						size="icon"
						variant="ghost"
						className="h-6 w-6"
						onClick={() => {
							hideGuidance();
							setShowGuidance(false);
						}}
					>
						<X className="h-3 w-3" />
					</Button>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground mb-3">
					This is a test guidance panel with simple floating UI.
				</p>
				<div className="space-y-2">
					<div className="flex items-center gap-2 text-sm">
						<span className="w-5 h-5 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
							1
						</span>
						<span>First step explanation</span>
					</div>
					<div className="flex items-center gap-2 text-sm">
						<span className="w-5 h-5 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
							2
						</span>
						<span>Second step explanation</span>
					</div>
				</div>
			</CardContent>
		</Card>
	), []);

	const { show: showGuidancePanel, hide: hideGuidance, isVisible: guidanceVisible } = useSimpleFloatingUI(
		'test-guidance',
		guidanceContent,
		{
			position: { bottom: 80, right: 16 },
			zIndex: 1100,
		}
	);

	const createNotification = () => {
		const count = notificationCount + 1;
		setNotificationCount(count);
		showNotification();
	};

	const visibleElements = getVisibleElements();

	return (
		<div className="container mx-auto p-6 space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Simple Floating UI System Test</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{/* Notification Tests */}
						<div className="space-y-2">
							<h4 className="font-medium">Notifications</h4>
							<Button onClick={createNotification} className="w-full">
								<Bell className="h-4 w-4 mr-2" />
								Create Notification
							</Button>
							<Button 
								onClick={notificationVisible ? hideNotification : showNotification}
								variant="outline" 
								className="w-full"
							>
								{notificationVisible ? 'Hide' : 'Show'} Current
							</Button>
							<p className="text-sm text-muted-foreground">
								Created: {notificationCount}
							</p>
						</div>

						{/* Settings Tests */}
						<div className="space-y-2">
							<h4 className="font-medium">Settings Panel</h4>
							<Button
								onClick={() => {
									if (showSettings) {
										hideSettings();
										setShowSettings(false);
									} else {
										showSettingsPanel();
										setShowSettings(true);
									}
								}}
								variant={showSettings ? 'default' : 'outline'}
								className="w-full"
							>
								<Settings className="h-4 w-4 mr-2" />
								{showSettings ? 'Hide' : 'Show'} Settings
							</Button>
							<p className="text-sm text-muted-foreground">
								Visible: {settingsVisible ? 'Yes' : 'No'}
							</p>
						</div>

						{/* Guidance Tests */}
						<div className="space-y-2">
							<h4 className="font-medium">Page Guidance</h4>
							<Button
								onClick={() => {
									if (showGuidance) {
										hideGuidance();
										setShowGuidance(false);
									} else {
										showGuidancePanel();
										setShowGuidance(true);
									}
								}}
								variant={showGuidance ? 'default' : 'outline'}
								className="w-full"
							>
								<HelpCircle className="h-4 w-4 mr-2" />
								{showGuidance ? 'Hide' : 'Show'} Guidance
							</Button>
							<p className="text-sm text-muted-foreground">
								Visible: {guidanceVisible ? 'Yes' : 'No'}
							</p>
						</div>
					</div>

					{/* Global Controls */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-3">Global Controls</h4>
						<div className="flex flex-wrap gap-2">
							<Button size="sm" variant="outline" onClick={() => hideAll()}>
								Hide All
							</Button>
						</div>
					</div>

					{/* Status */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-2">System Status</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>Visible Elements: {visibleElements.length}</p>
							<p>Element IDs: {visibleElements.map(el => el.id).join(', ') || 'None'}</p>
						</div>
					</div>

					{/* Instructions */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-2">Test Results</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>✅ No infinite loops detected</p>
							<p>✅ Smooth performance</p>
							<p>✅ Stable state management</p>
							<p>✅ Proper cleanup on unmount</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
