'use client';

import { But<PERSON> } from '@/components/ui';
import {
	useDOMFloating,
	hideAllDOMFloating,
	getVisibleDOMFloatingCount,
} from '@/hooks/use-dom-floating';
import { Settings } from 'lucide-react';

export default function MinimalTestPage() {
	// Completely static content to test
	const staticContent = (
		<div className="bg-background border rounded-lg shadow-lg p-4">
			<h3 className="text-lg font-semibold mb-2">Static Floating Element</h3>
			<p className="text-sm text-muted-foreground">
				This content never changes to test infinite loop issues.
			</p>
		</div>
	);

	const { show, hide, toggle, isVisible } = useDOMFloating('minimal-test', staticContent, {
		position: { top: 20, right: 20 },
		zIndex: 1100,
	});

	return (
		<div className="container mx-auto p-6">
			<h1 className="text-2xl font-bold mb-4">Minimal Test</h1>
			<p className="text-muted-foreground mb-4">
				Testing floating UI with completely static content.
			</p>
			<div className="space-x-2">
				<Button onClick={show}>
					<Settings className="h-4 w-4 mr-2" />
					Show
				</Button>
				<Button onClick={hide} variant="outline">
					Hide
				</Button>
				<Button onClick={toggle} variant="outline">
					Toggle
				</Button>
			</div>
			<p className="mt-4 text-sm text-muted-foreground">
				Visible: {isVisible ? 'Yes' : 'No'}
			</p>
		</div>
	);
}
