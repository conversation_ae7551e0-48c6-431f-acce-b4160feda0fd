'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';
import {
	useFloatingUIElement,
	useFloatingNotification,
	useFloatingTooltip,
	useFloatingModal,
	useFloatingUIManager,
} from '@/hooks/use-floating-ui';
import { Bell, HelpCircle, Settings, Info, X, Plus, Trash2, Eye, EyeOff } from 'lucide-react';

export function FloatingUIExample() {
	const [notificationCount, setNotificationCount] = useState(0);
	const [modalContent, setModalContent] = useState('');
	const [showTooltip, setShowTooltip] = useState(false);

	const {
		hideAll,
		showAll,
		hideByType,
		showByType,
		visibleCount,
		totalCount,
		getVisibleElements,
	} = useFloatingUIManager();

	// Example notification
	const notificationContent = (
		<Card className="w-80 shadow-lg">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center gap-2 text-sm">
					<Bell className="h-4 w-4" />
					Notification #{notificationCount}
				</CardTitle>
			</CardHeader>
			<CardContent>
				<p className="text-sm text-muted-foreground">
					This is a floating notification example. It can contain any content and will be
					positioned automatically.
				</p>
				<div className="flex gap-2 mt-3">
					<Button size="sm" variant="outline" onClick={() => hideNotification()}>
						Dismiss
					</Button>
					<Button size="sm" onClick={() => createNotification()}>
						Create Another
					</Button>
				</div>
			</CardContent>
		</Card>
	);

	const { show: showNotification, hide: hideNotification } = useFloatingNotification(
		`notification-${notificationCount}`,
		notificationContent
	);

	// Example tooltip
	const tooltipContent = (
		<div className="bg-popover text-popover-foreground p-2 rounded-md shadow-md border max-w-xs">
			<p className="text-sm">
				This is a floating tooltip. It appears when you hover or click and can contain
				helpful information.
			</p>
		</div>
	);

	const { show: showTooltipElement, hide: hideTooltipElement } = useFloatingTooltip(
		'example-tooltip',
		tooltipContent,
		{
			coordinates: { top: 100, left: 200 },
		}
	);

	// Example modal
	const modalContentElement = (
		<div className="bg-background border rounded-lg shadow-xl p-6 w-96">
			<div className="flex items-center justify-between mb-4">
				<h3 className="text-lg font-semibold">Floating Modal</h3>
				<Button size="icon" variant="ghost" onClick={() => hideModal()}>
					<X className="h-4 w-4" />
				</Button>
			</div>
			<div className="space-y-4">
				<p className="text-sm text-muted-foreground">
					{modalContent ||
						'This is a floating modal example. It can contain forms, content, or any other UI elements.'}
				</p>
				<div className="flex gap-2">
					<Button size="sm" variant="outline" onClick={() => hideModal()}>
						Cancel
					</Button>
					<Button size="sm" onClick={() => hideModal()}>
						Confirm
					</Button>
				</div>
			</div>
		</div>
	);

	const { show: showModal, hide: hideModal } = useFloatingModal(
		'example-modal',
		modalContentElement
	);

	// Example custom floating element
	const customContent = (
		<div className="bg-primary text-primary-foreground p-4 rounded-lg shadow-lg">
			<div className="flex items-center gap-2 mb-2">
				<Info className="h-4 w-4" />
				<span className="font-medium">Custom Floating Element</span>
			</div>
			<p className="text-sm opacity-90">
				This is a custom floating element with high priority. It will appear above other
				elements.
			</p>
		</div>
	);

	const {
		show: showCustom,
		hide: hideCustom,
		isVisible: customVisible,
	} = useFloatingUIElement('custom-element', customContent, {
		type: 'custom',
		priority: 'high',
		position: 'top-left',
		coordinates: { top: 20, left: 20 },
		animation: { type: 'bounce', duration: 400 },
	});

	// Helper functions
	const createNotification = () => {
		const newCount = notificationCount + 1;
		setNotificationCount(newCount);

		// Note: In a real implementation, you would manage notifications through the context
		// This is just for demo purposes
		console.log(`Would create notification #${newCount}`);
	};

	const showModalWithContent = (content: string) => {
		setModalContent(content);
		showModal();
	};

	const toggleTooltip = () => {
		if (showTooltip) {
			hideTooltipElement();
		} else {
			showTooltipElement();
		}
		setShowTooltip(!showTooltip);
	};

	return (
		<div className="space-y-6 p-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Settings className="h-5 w-5" />
						Floating UI System Demo
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{/* Notification Controls */}
						<div className="space-y-2">
							<h4 className="font-medium">Notifications</h4>
							<div className="space-y-2">
								<Button size="sm" onClick={showNotification} className="w-full">
									<Bell className="h-4 w-4 mr-2" />
									Show Notification
								</Button>
								<Button
									size="sm"
									variant="outline"
									onClick={createNotification}
									className="w-full"
								>
									<Plus className="h-4 w-4 mr-2" />
									Create New
								</Button>
							</div>
						</div>

						{/* Modal Controls */}
						<div className="space-y-2">
							<h4 className="font-medium">Modals</h4>
							<div className="space-y-2">
								<Button
									size="sm"
									onClick={() => showModalWithContent('Basic modal content')}
									className="w-full"
								>
									Show Modal
								</Button>
								<Button
									size="sm"
									variant="outline"
									onClick={() =>
										showModalWithContent('This is a modal with custom content!')
									}
									className="w-full"
								>
									Custom Modal
								</Button>
							</div>
						</div>

						{/* Tooltip Controls */}
						<div className="space-y-2">
							<h4 className="font-medium">Tooltips & Custom</h4>
							<div className="space-y-2">
								<Button size="sm" onClick={toggleTooltip} className="w-full">
									<HelpCircle className="h-4 w-4 mr-2" />
									{showTooltip ? 'Hide' : 'Show'} Tooltip
								</Button>
								<Button
									size="sm"
									variant="outline"
									onClick={customVisible ? hideCustom : showCustom}
									className="w-full"
								>
									{customVisible ? (
										<EyeOff className="h-4 w-4 mr-2" />
									) : (
										<Eye className="h-4 w-4 mr-2" />
									)}
									{customVisible ? 'Hide' : 'Show'} Custom
								</Button>
							</div>
						</div>
					</div>

					{/* Global Controls */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-3">Global Controls</h4>
						<div className="flex flex-wrap gap-2">
							<Button size="sm" variant="outline" onClick={() => showAll()}>
								<Eye className="h-4 w-4 mr-2" />
								Show All
							</Button>
							<Button size="sm" variant="outline" onClick={() => hideAll()}>
								<EyeOff className="h-4 w-4 mr-2" />
								Hide All
							</Button>
							<Button
								size="sm"
								variant="outline"
								onClick={() => hideByType('notification')}
							>
								<Trash2 className="h-4 w-4 mr-2" />
								Hide Notifications
							</Button>
							<Button
								size="sm"
								variant="outline"
								onClick={() => showByType('notification')}
							>
								<Bell className="h-4 w-4 mr-2" />
								Show Notifications
							</Button>
						</div>
					</div>

					{/* Status */}
					<div className="border-t pt-4">
						<h4 className="font-medium mb-2">Status</h4>
						<div className="text-sm text-muted-foreground space-y-1">
							<p>Visible Elements: {visibleCount}</p>
							<p>Total Elements: {totalCount}</p>
							<p>
								Active Elements:{' '}
								{getVisibleElements()
									.map((el) => el.id)
									.join(', ') || 'None'}
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
