'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui';
import { useSimpleFloating } from '@/hooks/use-simple-floating';
import { Settings } from 'lucide-react';

export default function DebugFloatingPage() {
	const [count, setCount] = useState(0);

	// Simple static content to test
	const simpleContent = useMemo(
		() => (
			<div className="bg-background border rounded-lg shadow-lg p-4">
				<h3 className="text-lg font-semibold mb-2">Debug Floating UI</h3>
				<p className="text-sm text-muted-foreground mb-3">
					This is a simple test to debug infinite loops.
				</p>
				<p className="text-sm">Count: {count}</p>
				<Button size="sm" onClick={() => setCount((c) => c + 1)}>
					Increment
				</Button>
			</div>
		),
		[count]
	);

	// Use simple floating UI to test
	const { toggle } = useSimpleFloating('debug-floating', simpleContent, {
		position: { top: 20, right: 20 },
		autoShow: true,
	});

	return (
		<div className="container mx-auto p-6">
			<h1 className="text-2xl font-bold mb-4">Debug Floating UI</h1>
			<p className="text-muted-foreground mb-4">
				This page tests the floating UI system with minimal setup to debug infinite loops.
			</p>
			<div className="space-x-2">
				<Button onClick={() => setCount((c) => c + 1)}>
					<Settings className="h-4 w-4 mr-2" />
					Increment Count: {count}
				</Button>
				<Button onClick={toggle} variant="outline">
					Toggle Floating Element
				</Button>
			</div>
		</div>
	);
}
