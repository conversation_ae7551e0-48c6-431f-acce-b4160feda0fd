import { ReactNode } from 'react';

// ============================================================================
// FLOATING UI TYPES
// ============================================================================

export type FloatingUIType = 
	| 'settings' 
	| 'guidance' 
	| 'notification' 
	| 'tooltip' 
	| 'modal' 
	| 'dropdown'
	| 'custom';

export type FloatingPosition = 
	| 'top-left' 
	| 'top-right' 
	| 'bottom-left' 
	| 'bottom-right' 
	| 'center'
	| 'custom';

export type FloatingPriority = 'low' | 'medium' | 'high' | 'critical';

export interface FloatingCoordinates {
	top?: number;
	bottom?: number;
	left?: number;
	right?: number;
}

export interface FloatingDimensions {
	width?: number;
	height?: number;
	minWidth?: number;
	minHeight?: number;
	maxWidth?: number;
	maxHeight?: number;
}

export interface FloatingAnimationConfig {
	type?: 'fade' | 'slide' | 'scale' | 'bounce' | 'none';
	duration?: number;
	delay?: number;
	easing?: string;
}

export interface FloatingElement {
	id: string;
	type: FloatingUIType;
	priority: FloatingPriority;
	position: FloatingPosition;
	coordinates?: FloatingCoordinates;
	dimensions?: FloatingDimensions;
	zIndex?: number;
	visible: boolean;
	persistent?: boolean; // Không bị ẩn khi có collision
	collisionDetection?: boolean;
	animation?: FloatingAnimationConfig;
	content: ReactNode;
	className?: string;
	style?: React.CSSProperties;
	onShow?: () => void;
	onHide?: () => void;
	onCollision?: (collidingElements: FloatingElement[]) => void;
}

export interface FloatingUIState {
	elements: Map<string, FloatingElement>;
	activeElements: string[];
	collisions: Map<string, string[]>;
	viewport: {
		width: number;
		height: number;
	};
}

export interface FloatingUIActions {
	register: (element: Omit<FloatingElement, 'visible'>) => void;
	unregister: (id: string) => void;
	show: (id: string) => void;
	hide: (id: string) => void;
	toggle: (id: string) => void;
	update: (id: string, updates: Partial<FloatingElement>) => void;
	bringToFront: (id: string) => void;
	sendToBack: (id: string) => void;
	hideAll: (except?: string[]) => void;
	showAll: () => void;
	getElement: (id: string) => FloatingElement | undefined;
	getVisibleElements: () => FloatingElement[];
	getElementsByType: (type: FloatingUIType) => FloatingElement[];
	getElementsByPriority: (priority: FloatingPriority) => FloatingElement[];
	checkCollisions: (elementId: string) => string[];
	resolveCollisions: (elementId: string) => void;
	calculateOptimalPosition: (element: FloatingElement) => FloatingCoordinates;
}

export interface FloatingUIContextType extends FloatingUIActions {
	state: FloatingUIState;
}

// ============================================================================
// FLOATING UI CONFIGURATION
// ============================================================================

export interface FloatingUIConfig {
	defaultZIndex: number;
	zIndexStep: number;
	collisionMargin: number;
	animationDefaults: FloatingAnimationConfig;
	responsiveBreakpoints: {
		mobile: number;
		tablet: number;
		desktop: number;
	};
	positionDefaults: Record<FloatingPosition, FloatingCoordinates>;
	priorityZIndex: Record<FloatingPriority, number>;
}

export const DEFAULT_FLOATING_CONFIG: FloatingUIConfig = {
	defaultZIndex: 1000,
	zIndexStep: 10,
	collisionMargin: 8,
	animationDefaults: {
		type: 'fade',
		duration: 200,
		delay: 0,
		easing: 'ease-in-out',
	},
	responsiveBreakpoints: {
		mobile: 768,
		tablet: 1024,
		desktop: 1280,
	},
	positionDefaults: {
		'top-left': { top: 16, left: 16 },
		'top-right': { top: 16, right: 16 },
		'bottom-left': { bottom: 16, left: 16 },
		'bottom-right': { bottom: 16, right: 16 },
		'center': { top: 50, left: 50 },
		'custom': {},
	},
	priorityZIndex: {
		low: 1000,
		medium: 1100,
		high: 1200,
		critical: 1300,
	},
};

// ============================================================================
// FLOATING UI HOOKS TYPES
// ============================================================================

export interface UseFloatingUIOptions {
	type: FloatingUIType;
	priority?: FloatingPriority;
	position?: FloatingPosition;
	coordinates?: FloatingCoordinates;
	dimensions?: FloatingDimensions;
	persistent?: boolean;
	collisionDetection?: boolean;
	animation?: FloatingAnimationConfig;
	className?: string;
	style?: React.CSSProperties;
	autoShow?: boolean;
	onShow?: () => void;
	onHide?: () => void;
	onCollision?: (collidingElements: FloatingElement[]) => void;
}

export interface UseFloatingUIReturn {
	show: () => void;
	hide: () => void;
	toggle: () => void;
	update: (updates: Partial<FloatingElement>) => void;
	isVisible: boolean;
	element: FloatingElement | undefined;
	position: FloatingCoordinates;
	zIndex: number;
	hasCollisions: boolean;
	collidingElements: FloatingElement[];
}

// ============================================================================
// FLOATING UI MANAGER TYPES
// ============================================================================

export interface FloatingUIManagerProps {
	children?: ReactNode;
	config?: Partial<FloatingUIConfig>;
	className?: string;
	style?: React.CSSProperties;
}

// ============================================================================
// SPECIFIC FLOATING UI TYPES
// ============================================================================

export interface FloatingSettingsOptions extends Omit<UseFloatingUIOptions, 'type'> {
	showLanguageSelector?: boolean;
	showThemeSelector?: boolean;
	showAuthActions?: boolean;
}

export interface FloatingGuidanceOptions extends Omit<UseFloatingUIOptions, 'type'> {
	titleKey: string;
	steps: Array<{
		key: string;
		icon?: React.ComponentType<any>;
	}>;
	tipKey?: string;
	requirementKey?: string;
	defaultOpen?: boolean;
}
